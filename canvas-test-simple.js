// 简化的Canvas文本提取测试脚本
// 在浏览器控制台中运行此脚本来快速测试Canvas文本提取

(function() {
  console.log('=== 简化Canvas测试开始 ===');
  
  // 检查是否已经有Canvas文本拦截
  function checkCanvasHook() {
    console.log('检查Canvas文本拦截状态...');
    
    // 检查全局变量
    if (window._canvasTextHooked) {
      console.log('✅ Canvas文本拦截已启用');
    } else {
      console.log('❌ Canvas文本拦截未启用');
    }
    
    // 检查是否有textOperations
    const frames = document.querySelectorAll('iframe');
    let totalTextOps = 0;
    
    frames.forEach((frame, i) => {
      try {
        if (frame.contentWindow && frame.contentWindow.textOperations) {
          const ops = frame.contentWindow.textOperations;
          console.log(`iframe ${i}: 捕获到 ${ops.length} 个文本操作`);
          totalTextOps += ops.length;
          
          if (ops.length > 0) {
            console.log(`iframe ${i} 文本示例:`, ops.slice(0, 3));
          }
        }
      } catch (e) {
        console.log(`iframe ${i}: 无法访问 (${e.message})`);
      }
    });
    
    console.log(`总计捕获文本操作: ${totalTextOps}`);
    return totalTextOps > 0;
  }
  
  // 查找Canvas元素
  function findCanvasElements() {
    console.log('查找Canvas元素...');
    
    const mainCanvases = document.querySelectorAll('canvas');
    console.log(`主页面Canvas数量: ${mainCanvases.length}`);
    
    const frames = document.querySelectorAll('iframe');
    console.log(`iframe数量: ${frames.length}`);
    
    let totalCanvases = mainCanvases.length;
    
    frames.forEach((frame, i) => {
      try {
        if (frame.contentDocument) {
          const frameCanvases = frame.contentDocument.querySelectorAll('canvas');
          console.log(`iframe ${i} (${frame.src}): ${frameCanvases.length} 个Canvas`);
          totalCanvases += frameCanvases.length;
          
          // 检查Canvas尺寸
          frameCanvases.forEach((canvas, j) => {
            console.log(`  Canvas ${j}: ${canvas.width}x${canvas.height}`);
          });
        }
      } catch (e) {
        console.log(`iframe ${i}: 无法访问 (${e.message})`);
      }
    });
    
    console.log(`总Canvas数量: ${totalCanvases}`);
    return totalCanvases;
  }
  
  // 尝试手动触发Canvas文本提取
  function triggerCanvasExtraction() {
    console.log('尝试手动触发Canvas文本提取...');
    
    const frames = document.querySelectorAll('iframe');
    
    frames.forEach((frame, i) => {
      try {
        if (frame.contentDocument) {
          const canvases = frame.contentDocument.querySelectorAll('canvas');
          
          canvases.forEach((canvas, j) => {
            try {
              // 尝试获取2D上下文
              const ctx = canvas.getContext('2d');
              if (ctx) {
                console.log(`iframe ${i} Canvas ${j}: 成功获取2D上下文`);
                
                // 尝试触发重绘
                const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                ctx.putImageData(imageData, 0, 0);
                console.log(`iframe ${i} Canvas ${j}: 触发重绘完成`);
              }
            } catch (e) {
              console.warn(`iframe ${i} Canvas ${j}: 处理失败 (${e.message})`);
            }
          });
        }
      } catch (e) {
        console.warn(`iframe ${i}: 无法访问 (${e.message})`);
      }
    });
  }
  
  // 等待并检查结果
  async function waitAndCheck() {
    console.log('等待3秒后检查结果...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    const hasText = checkCanvasHook();
    if (hasText) {
      console.log('✅ Canvas文本提取成功！');
    } else {
      console.log('❌ 未检测到Canvas文本，可能需要：');
      console.log('1. 等待页面完全加载');
      console.log('2. 滚动页面触发Canvas重绘');
      console.log('3. 检查是否有权限访问iframe');
    }
  }
  
  // 主测试函数
  async function runTest() {
    console.log('开始Canvas文本提取测试...');
    
    // 1. 检查当前状态
    checkCanvasHook();
    
    // 2. 查找Canvas元素
    const canvasCount = findCanvasElements();
    
    if (canvasCount === 0) {
      console.log('❌ 未找到任何Canvas元素');
      return;
    }
    
    // 3. 手动触发提取
    triggerCanvasExtraction();
    
    // 4. 等待并检查结果
    await waitAndCheck();
    
    console.log('测试完成！');
  }
  
  // 导出到全局
  window.testCanvasExtraction = runTest;
  window.checkCanvasHook = checkCanvasHook;
  window.findCanvasElements = findCanvasElements;
  
  console.log('测试脚本加载完成！');
  console.log('运行 testCanvasExtraction() 开始测试');
  console.log('或运行 checkCanvasHook() 检查当前状态');
})();
