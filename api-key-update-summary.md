# API密钥更新总结

## 修改内容

已成功将默认API密钥从 `'trial-token-123'` 更新为 `'maycur'`。

## 修改的文件

### 1. popup.js
**位置：** 第123行
**修改前：**
```javascript
const defaultApiKey = 'trial-token-123';
```
**修改后：**
```javascript
const defaultApiKey = 'maycur';
```

### 2. popup.html
**位置：** 第327-329行
**修改前：**
```html
<input type="text" id="apiKey" placeholder="请输入您的API密钥" value="trial-token-123">
<div class="tooltip">
  试用密钥：trial-token-123，获取专享API密钥请访问：<a href='https://hrassistent.online' target='_blank'>https://hrassistent.online</a> 注册账号获取
</div>
```
**修改后：**
```html
<input type="text" id="apiKey" placeholder="请输入您的API密钥" value="maycur">
<div class="tooltip">
  试用密钥：maycur，获取专享API密钥请访问：<a href='https://hrassistent.online' target='_blank'>https://hrassistent.online</a> 注册账号获取
</div>
```

## 影响范围

1. **新用户体验：** 新安装扩展的用户将默认使用 `'maycur'` 作为API密钥
2. **现有用户：** 已经设置了自定义API密钥的用户不会受到影响
3. **界面显示：** 设置页面中的默认值和提示文本已更新

## 验证方法

1. **清除扩展数据：** 在Chrome扩展管理页面清除扩展的存储数据
2. **重新加载扩展：** 重新加载扩展
3. **打开设置页面：** 检查API密钥输入框是否显示 `'maycur'`
4. **检查提示文本：** 确认提示文本中显示的试用密钥为 `'maycur'`

## 注意事项

- 修改只影响默认值，不会覆盖用户已保存的自定义API密钥
- 如果用户之前使用的是默认的 `'trial-token-123'`，需要手动更新或清除扩展数据重新初始化
- 建议在发布新版本时在更新说明中提及此变更

## 相关功能

这些修改确保了：
- 扩展初始化时使用新的默认密钥
- 用户界面显示正确的默认值
- 提示信息与实际默认值保持一致
- 向后兼容性（不影响已设置自定义密钥的用户）
