# Canvas 简历内容提取解决方案

## 问题描述
网页中的简历内容被包裹在 Canvas 元素中，无法直接通过 DOM 操作获取文本内容。

## 解决方案

### 1. Canvas 文本操作拦截（推荐）
通过重写 Canvas 的 `fillText` 和 `strokeText` 方法来捕获绘制的文本内容。

**优点：**
- 准确性高，能获取到原始文本
- 性能好，实时捕获
- 保持文本的位置信息

**实现方式：**
```javascript
// 重写Canvas的getContext方法
HTMLCanvasElement.prototype.getContext = function(contextType) {
  const context = originalGetContext.apply(this, arguments);
  
  if (context && contextType === '2d') {
    // 拦截fillText和strokeText方法
    context.fillText = function(text, x, y) {
      // 保存文本信息
      window.canvasTextOperations.push({text, x, y, type: 'fillText'});
      return originalFillText.apply(this, arguments);
    };
  }
  return context;
};
```

### 2. OCR 识别（备用方案）
当文本拦截失败时，使用 OCR 技术识别 Canvas 中的图像内容。

**优点：**
- 兼容性好，适用于各种Canvas实现
- 可以处理图片形式的文本

**缺点：**
- 准确性依赖OCR服务质量
- 性能较差，需要网络请求
- 可能有识别错误

### 3. 混合方案（当前实现）
优先使用文本拦截，失败时自动降级到OCR识别。

## 使用方法

### 1. 调试Canvas提取
在浏览器控制台中运行：
```javascript
// 加载调试脚本
const script = document.createElement('script');
script.src = 'canvas-debug.js';
document.head.appendChild(script);

// 运行调试
debugCanvasExtraction();
```

### 2. 手动测试
```javascript
// 检查是否有Canvas元素
const canvases = document.querySelectorAll('canvas');
console.log('找到Canvas数量:', canvases.length);

// 检查iframe中的Canvas
const iframes = document.querySelectorAll('iframe');
iframes.forEach((iframe, i) => {
  try {
    const canvases = iframe.contentDocument.querySelectorAll('canvas');
    console.log(`iframe ${i} 中的Canvas数量:`, canvases.length);
  } catch (e) {
    console.log(`iframe ${i} 无法访问:`, e.message);
  }
});
```

## 常见问题及解决方案

### 1. 找不到Canvas元素
**原因：** Canvas可能在嵌套的iframe中
**解决：** 递归查找所有iframe中的Canvas元素

### 2. 脚本注入失败
**原因：** 跨域限制或iframe未加载完成
**解决：** 
- 等待iframe加载完成
- 使用content script注入
- 检查同源策略

### 3. 文本拦截无效
**原因：** Canvas文本在脚本注入前已经绘制完成
**解决：**
- 尽早注入脚本
- 触发Canvas重绘
- 使用OCR备用方案

### 4. 文本顺序混乱
**原因：** Canvas文本绘制顺序与阅读顺序不一致
**解决：**
- 按y坐标分组
- 同一行内按x坐标排序
- 智能文本重组

## 代码更新说明

### 主要改进：
1. **多选择器支持：** 支持多种iframe选择器，提高兼容性
2. **增强的脚本注入：** 更强大的Canvas文本捕获脚本
3. **备用OCR方案：** 当文本拦截失败时自动使用OCR
4. **调试功能：** 提供详细的调试信息和测试工具
5. **错误处理：** 更好的错误处理和降级机制

### 关键函数：
- `extractResumeCanvas()`: 主要的Canvas文本提取函数
- `tryExtractCanvasFromCurrentFrame()`: 直接从当前iframe提取
- `injectEnhancedCanvasCaptureScript()`: 注入增强的捕获脚本
- `tryOCRExtraction()`: OCR备用方案
- `debugCanvasExtraction()`: 调试工具

## 测试步骤

1. 打开包含Canvas简历的页面
2. 在控制台运行调试脚本
3. 检查输出的调试信息
4. 根据问题调整配置
5. 验证文本提取结果

## 性能优化建议

1. **延迟注入：** 在需要时才注入脚本
2. **缓存结果：** 避免重复提取同一Canvas
3. **异步处理：** 使用异步方法避免阻塞
4. **资源清理：** 及时清理不需要的监听器和数据

## 兼容性说明

- 支持Chrome、Firefox、Safari等主流浏览器
- 需要Canvas 2D上下文支持
- OCR功能需要网络连接
- 某些安全策略可能影响iframe访问
