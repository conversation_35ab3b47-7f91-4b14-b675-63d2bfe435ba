// 在简历iframe页面中执行的脚本
(function() {
  if (!window.__RESUME_FRAME_SCRIPT_LOG) {
    window.__RESUME_FRAME_SCRIPT_LOG = true;
    console.log('iframe脚本通过manifest或动态注入执行:', window.location.href);
  }

  console.log('iframe脚本开始执行');

  // 最基础的状态管理
  var textOperations = [];
  var isScrolling = false;
  var textSent = false;
  var currentRequestId = null;
  var scrollCompleteListener = null;
  var forceSendTimer = null;

  // 监听来自父页面的消息
  window.addEventListener('message', function(event) {
    if (event.data && event.data.type === 'setRequestId') {
      currentRequestId = event.data.requestId;
      console.log('收到请求ID:', currentRequestId);
    }
  });

  // 1. 全局 hook fillText/strokeText
  if (!window._canvasTextHooked) {
    window._canvasTextHooked = true;
    var origFillText = CanvasRenderingContext2D.prototype.fillText;
    CanvasRenderingContext2D.prototype.fillText = function(text, x, y) {
      try {
        // console.log('fillText:', text, x, y);
        if (text && typeof text === 'string' && text.trim().length > 0) {
          textOperations.push({
            text: text.trim(),
            x: Number(x) || 0,
            y: Number(y) || 0,
            timestamp: Date.now()
          });
        }
      } catch (e) {
        console.warn('[HOOK] fillText处理失败:', e);
      }
      return origFillText.apply(this, arguments);
    };
    if (CanvasRenderingContext2D.prototype.strokeText) {
      var origStrokeText = CanvasRenderingContext2D.prototype.strokeText;
      CanvasRenderingContext2D.prototype.strokeText = function(text, x, y) {
        try {
          if (text && typeof text === 'string' && text.trim().length > 0) {
            textOperations.push({
              text: text.trim(),
              x: Number(x) || 0,
              y: Number(y) || 0,
              timestamp: Date.now()
            });
          }
        } catch (e) {
          console.warn('[HOOK] strokeText处理失败:', e);
        }
        return origStrokeText.apply(this, arguments);
      };
    }
    console.log('[HOOK] CanvasRenderingContext2D.prototype.fillText/strokeText 全局hook完成');
  }

  // 2. 主动遍历所有canvas，触发 getContext('2d')
  function triggerAllCanvasGetContext() {
    try {
      var canvases = document.querySelectorAll('canvas');
      console.log('[HOOK] 找到', canvases.length, '个canvas元素');
      canvases.forEach(function(c, index) {
        try {
          if (c && typeof c.getContext === 'function') {
            c.getContext('2d');
            console.log('[HOOK] canvas', index, '已自动getContext("2d")');
          }
        } catch(e) {
          console.warn('[HOOK] canvas', index, 'getContext失败:', e);
        }
      });
      console.log('[HOOK] 已主动触发所有canvas的getContext("2d")');
    } catch (e) {
      console.error('[HOOK] 遍历canvas失败:', e);
    }
  }
  triggerAllCanvasGetContext();

  // 3. 监听DOM变化，发现新canvas自动getContext
  var canvasObserver = new MutationObserver(function(mutations) {
    try {
      mutations.forEach(function(mutation) {
        try {
          mutation.addedNodes.forEach(function(node) {
            try {
              if (node.nodeType === 1 && node.tagName === 'CANVAS') {
                if (node.getContext && typeof node.getContext === 'function') {
                  node.getContext('2d');
                  console.log('[HOOK] 新canvas已自动getContext("2d")');
                }
              }
              // 递归处理嵌套节点
              if (node.nodeType === 1 && node.querySelectorAll) {
                node.querySelectorAll('canvas').forEach(function(c) {
                  try {
                    if (c.getContext && typeof c.getContext === 'function') {
                      c.getContext('2d');
                      console.log('[HOOK] 嵌套canvas已自动getContext("2d")');
                    }
                  } catch(e) {
                    console.warn('[HOOK] 嵌套canvas处理失败:', e);
                  }
                });
              }
            } catch (e) {
              console.warn('[HOOK] 处理新增节点失败:', e);
            }
          });
        } catch (e) {
          console.warn('[HOOK] 处理mutation失败:', e);
        }
      });
    } catch (e) {
      console.error('[HOOK] MutationObserver回调失败:', e);
    }
  });
  // 安全地启动观察器
  try {
    if (document.body) {
      canvasObserver.observe(document.body, { childList: true, subtree: true });
      console.log('[HOOK] Canvas观察器已启动');
    } else {
      // 如果body还没有加载，等待DOM加载完成
      document.addEventListener('DOMContentLoaded', function() {
        try {
          if (document.body) {
            canvasObserver.observe(document.body, { childList: true, subtree: true });
            console.log('[HOOK] Canvas观察器已启动(DOMContentLoaded)');
          }
        } catch (e) {
          console.warn('[HOOK] 启动Canvas观察器失败(DOMContentLoaded):', e);
        }
      });
    }
  } catch (e) {
    console.warn('[HOOK] 启动Canvas观察器失败:', e);
  }

  // 自动滚动功能，循环滚动直到Canvas内容全部加载
  async function autoScroll() {
    if (isScrolling) return;
    isScrolling = true;
    let lastTextCount = textOperations.length;
    let maxTries = 10; // 最多尝试10次，防止死循环

    while (maxTries-- > 0) {
      window.parent.postMessage({
        type: 'resumeScrollRequest',
        source: 'resume-frame-script',
        requestId: currentRequestId,
        data: { fromIframe: window.location.href }
      }, '*');

      await new Promise((resolve) => {
        function handleScrollComplete(event) {
          if (event.data?.type === 'resumeScrollComplete' && event.data?.source === 'parent') {
            window.removeEventListener('message', handleScrollComplete);
            resolve();
          }
        }
        window.addEventListener('message', handleScrollComplete);
      });

      // 等待内容渲染
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 判断是否有新内容
      if (textOperations.length <= lastTextCount) {
        break; // 没有新内容，说明到底了
      }
      lastTextCount = textOperations.length;
    }

    isScrolling = false;
    if (!textSent) sendTextToParent();
  }

  console.log('textOperations内容:', JSON.stringify(textOperations));

  // 发送文本到父页面
  function sendTextToParent() {
    if (textSent) return;
    if (forceSendTimer) {
      clearTimeout(forceSendTimer);
      forceSendTimer = null;
    }
    // 按y坐标排序并去重
    var sortedOperations = textOperations
      .sort(function(a, b) { return a.y - b.y; })
      .filter(function(op, index, self) {
        return index === self.findIndex(function(t) {
          return t.text === op.text && Math.abs(t.y - op.y) < 5;
        });
      });
    var organizedText = sortedOperations
      .map(function(op) { return op.text; })
      .join('\n')
      .replace(/(?<=.)\n(?=.)/g, '')
      .trim();
    if (!organizedText) {
      console.warn('没有提取到有效文本内容');
    }
    var message = {
      type: 'resumeTextExtracted',
      source: 'resume-frame-script',
      requestId: currentRequestId,
      data: {
        text: organizedText,
        timestamp: new Date().toISOString(),
        textLength: organizedText.length,
        operationsCount: sortedOperations.length,
        fromIframe: window.location.href,
        isComplete: true
      }
    };
    try {
      window.parent.postMessage(message, '*');
      console.log('消息已发送到父窗口');
    } catch (e) {
      console.error('发送消息失败:', e);
    }
    textSent = true;
    cleanupResources();
  }

  // 清理资源
  function cleanupResources() {
    textOperations = [];
    if (scrollCompleteListener) {
      window.removeEventListener('message', scrollCompleteListener);
      scrollCompleteListener = null;
    }
    if (forceSendTimer) {
      clearTimeout(forceSendTimer);
      forceSendTimer = null;
    }
  }

  // 监听Canvas出现
  function safeObserveBody(observer) {
    function tryObserve() {
      const target = document.body;
      if (target instanceof Node) {
        observer.observe(target, { childList: true, subtree: true });
      } else {
        // console日志用中文
        console.log('MutationObserver 目标不是 Node，当前值：', target);
        setTimeout(tryObserve, 100); // 100ms后重试
      }
    }
    tryObserve();
  }

  var observer = new MutationObserver(function(mutations) {
    for (var i = 0; i < mutations.length; i++) {
      var mutation = mutations[i];
      if (mutation.type === 'childList') {
        var canvas = document.querySelector('canvas');
        if (canvas && !textSent && !isScrolling) {
          observer.disconnect();
          setTimeout(function() {
            if (!textSent) {
              if (textOperations.length > 0) {
                sendTextToParent();
              } else {
                sendTextToParent();
              }
            }
          }, 3000);
          break;
        }
      }
    }
  });

  safeObserveBody(observer);

  // 页面已加载完成立即检查
  if (document.readyState === 'complete') {
    var canvas = document.querySelector('canvas');
    if (canvas && !textSent && !isScrolling) {
      setTimeout(function() {
        if (!textSent) {
          if (textOperations.length > 0) {
            sendTextToParent();
          } else {
            sendTextToParent();
          }
        }
      }, 2000);
    }
  }

  // 超时保护
  forceSendTimer = setTimeout(function() {
    if (!textSent) {
      sendTextToParent();
    }
  }, 10000);

  // 每30秒清理一次textOperations中过期数据（只保留1分钟内的）
  setInterval(function() {
    var now = Date.now();
    textOperations = textOperations.filter(function(op) {
      return now - op.timestamp < 60000; // 只保留1分钟内的
    });
  }, 30000);
})(); 