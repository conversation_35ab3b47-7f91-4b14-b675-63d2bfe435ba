// Canvas 简历提取调试脚本
// 在浏览器控制台中运行此脚本来调试 Canvas 文本提取问题

(function() {
  console.log('=== Canvas 简历提取调试脚本 ===');
  
  // 查找所有可能的简历iframe
  function findResumeIframes() {
    const selectors = [
      'iframe[src*="/web/frame/c-resume/"]',
      'iframe[src*="/web/frame/recommend/"]',
      'iframe[src*="canvasResume"]',
      'iframe[id*="resume"]'
    ];
    
    const iframes = [];
    selectors.forEach(selector => {
      const found = document.querySelectorAll(selector);
      found.forEach(iframe => {
        if (!iframes.includes(iframe)) {
          iframes.push(iframe);
        }
      });
    });
    
    return iframes;
  }
  
  // 注入Canvas文本捕获脚本
  function injectCanvasScript(doc) {
    const script = doc.createElement('script');
    script.textContent = `
      (function() {
        console.log('注入Canvas文本捕获脚本...');
        
        if (!window.canvasTextOperations) {
          window.canvasTextOperations = [];
        }
        
        const originalGetContext = HTMLCanvasElement.prototype.getContext;
        
        HTMLCanvasElement.prototype.getContext = function(contextType) {
          const context = originalGetContext.apply(this, arguments);
          
          if (context && contextType === '2d' && !this._enhanced) {
            this._enhanced = true;
            console.log('增强Canvas:', this);
            
            const originalFillText = context.fillText;
            const originalStrokeText = context.strokeText;
            
            context.fillText = function(text, x, y) {
              console.log('捕获fillText:', text, 'at', x, y);
              window.canvasTextOperations.push({
                text: String(text),
                x: Number(x),
                y: Number(y),
                type: 'fillText'
              });
              return originalFillText.apply(this, arguments);
            };
            
            context.strokeText = function(text, x, y) {
              console.log('捕获strokeText:', text, 'at', x, y);
              window.canvasTextOperations.push({
                text: String(text),
                x: Number(x),
                y: Number(y),
                type: 'strokeText'
              });
              return originalStrokeText.apply(this, arguments);
            };
          }
          return context;
        };
        
        window.getCanvasText = function() {
          return window.canvasTextOperations || [];
        };
        
        window.clearCanvasText = function() {
          window.canvasTextOperations = [];
        };
        
        console.log('Canvas文本捕获脚本注入完成');
      })();
    `;
    
    doc.head.appendChild(script);
  }
  
  // 主调试函数
  async function debugCanvasExtraction() {
    console.log('开始调试Canvas文本提取...');
    
    const iframes = findResumeIframes();
    console.log(`找到 ${iframes.length} 个可能的简历iframe`);
    
    for (let i = 0; i < iframes.length; i++) {
      const iframe = iframes[i];
      console.log(`\n--- 处理iframe ${i}: ${iframe.src} ---`);
      
      try {
        if (!iframe.contentDocument) {
          console.warn('无法访问iframe的contentDocument');
          continue;
        }
        
        const doc = iframe.contentDocument;
        
        // 查找Canvas元素
        const canvases = doc.querySelectorAll('canvas');
        console.log(`找到 ${canvases.length} 个Canvas元素`);
        
        if (canvases.length === 0) {
          console.log('未找到Canvas元素，检查是否有嵌套iframe...');
          const nestedIframes = doc.querySelectorAll('iframe');
          console.log(`找到 ${nestedIframes.length} 个嵌套iframe`);
          
          for (let j = 0; j < nestedIframes.length; j++) {
            const nestedIframe = nestedIframes[j];
            console.log(`嵌套iframe ${j}: ${nestedIframe.src}`);
            
            try {
              if (nestedIframe.contentDocument) {
                const nestedCanvases = nestedIframe.contentDocument.querySelectorAll('canvas');
                console.log(`嵌套iframe ${j} 中找到 ${nestedCanvases.length} 个Canvas`);
                
                if (nestedCanvases.length > 0) {
                  // 注入脚本到嵌套iframe
                  injectCanvasScript(nestedIframe.contentDocument);
                  
                  // 等待脚本执行
                  await new Promise(resolve => setTimeout(resolve, 1000));
                  
                  // 检查文本操作
                  const textOps = nestedIframe.contentWindow.getCanvasText();
                  console.log(`嵌套iframe ${j} 捕获到 ${textOps.length} 个文本操作`);
                  
                  if (textOps.length > 0) {
                    console.log('文本操作示例:', textOps.slice(0, 10));
                    
                    // 尝试组织文本
                    const sortedText = textOps
                      .sort((a, b) => a.y - b.y || a.x - b.x)
                      .map(op => op.text)
                      .join(' ');
                    
                    console.log('组织后的文本:', sortedText.substring(0, 200) + '...');
                  }
                }
              }
            } catch (error) {
              console.error(`处理嵌套iframe ${j} 时出错:`, error);
            }
          }
        } else {
          // 直接处理Canvas
          injectCanvasScript(doc);
          
          // 等待脚本执行
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          // 检查文本操作
          const textOps = iframe.contentWindow.getCanvasText();
          console.log(`捕获到 ${textOps.length} 个文本操作`);
          
          if (textOps.length > 0) {
            console.log('文本操作示例:', textOps.slice(0, 10));
          }
        }
        
      } catch (error) {
        console.error(`处理iframe ${i} 时出错:`, error);
      }
    }
    
    console.log('\n调试完成！');
  }
  
  // 导出调试函数到全局
  window.debugCanvasExtraction = debugCanvasExtraction;
  
  console.log('调试脚本加载完成！运行 debugCanvasExtraction() 开始调试');
})();
